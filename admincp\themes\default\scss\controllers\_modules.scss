#module-list {
  h3 {
    position: relative;
    top: 0;
    padding: 0;
    margin: 0;
    color: $main-color;
    background-color: transparent;
    @include left(0);
  }

  select.active {
    background: lighten($brand-primary, 40%);
    border: solid 1px $brand-primary;

    option {
      background: #fff !important;
    }
  }
}

.hook_panel {
  margin-bottom: 10px !important;
  border: solid #e6e6e6 1px;
  @include box-shadow(rgba(black,0.1) 0 -2px 0 inset);
  @include border-radius(3px);
  @include padding(10px, 10px, 5px, 10px);
}

.module_name {
  font-size: 1.2em;
}

.module_description {
  @extend .text-muted;
}

.hook_panel_header {
  @include margin(0, -10px, 0, -10px);
  @include padding(0, 10px, 10px, 10px);

  .hook_name {
    padding: 0 4px;
    font-size: 1.4em;
    color: $brand-primary;
    background-color: #fff;
    @include border-radius(3px);
  }
}

.hook_title {
  @extend .text-muted;
}

.hook_description {
  @extend .text-muted;
  @include padding(3px, 0, 0, 3px);
}

.modules_list_container_tab {
  img {
    max-width: 66px;
  }
}

.module_list {
  .module_list_item {
    display: table;
    width: 100%;
    padding: 5px 0;
    margin-bottom: -1px;
    border: solid 1px #9ed0ec;

    &.highlight {
      background-color: #f7e69f;
    }
  }

  .draggable {
    cursor: pointer;
  }
}

.module_col_select {
  display: table-cell;
  width: 22px;
  min-height: 35px;
  text-align: center;
  vertical-align: middle;
  @include border-right(1px solid #ddd);
}

.module_col_position {
  display: table-cell;
  width: 70px;
  vertical-align: middle;
  @include text-align(right);

  .positions {
    padding: 0 5px;
    font-size: 1.4em;
    color: #aaa;
    text-shadow: #fff 1px 1px;
    cursor: move;
    background-color: #eee;
    border: solid 1px #ccc;
    @include border-radius(3px);
    @include box-shadow(rgba(0,0,0,0.2) 0 1px 3px inset);
  }
}

.module_col_icon {
  display: table-cell;
  width: 75px;
  text-align: center;
  vertical-align: middle;
}

.module_col_infos {
  display: table-cell;
  height: 50px;
  vertical-align: middle;
}

.module_col_actions {
  display: table-cell;
  width: 160px;
  padding: 0 10px;
  vertical-align: middle;
  @include text-align(right);

  .btn-group {
    @include text-align(left);
  }
}

li.sortable-placeholder {
  margin: 4px;
  background-color: #eee;
  border: 1px dashed #ccc;
  @include border-radius(5px);
}

td.module_active {
  background-color: lighten($brand-success, 10%) !important;
}

td.module_inactive {
  background-color: lighten($gray, 50%) !important;
}

.module-badge-popular,
.module-badge-partner,
.module-badge-bought {
  @extend .badge;
  font-size: 12px;
}

.module-badge-popular {
  background-color: $brand-addons;
}

.module-badge-partner {
  background-color: $brand-primary;
}

.module-badge-bought {
  background-color: $greenPrestashop;
}

.categoriesTitle {
  .list-group {
    #filternameForm {
      @include padding-right(15px);
    }

    .list-group-item {
      position: relative;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      @include padding-right(35px);

      .badge {
        position: absolute;
        @include right(5px);
      }
    }
  }
}

.quickview-badge {
  margin-top: 30px;
}

.quickview-price {
  font-size: 1.8em;
  color: #666;
  @include float(right);
}
//stars
.rating {
  font-size: 20px;
  direction: rtl;
  unicode-bidi: bidi-override;

  span.star {
    display: inline-block;
    font-family: $icon-font-family;

    &::before {
      color: #bbb;
      content: "\f006";
      @include padding-right(3px);
    }

    &.active::before,
    &.active ~ span.star::before {
      color: #f5ab35;
      content: "\f005";
    }
  }
}

#modules_list_container_tab_modal {
  .img-thumbnail {
    max-width: 60px;
    max-height: 60px;
  }
}
