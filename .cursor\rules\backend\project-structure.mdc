---
description: 
globs: *Controller.php
alwaysApply: false
---
### 项目结构与导航

- **核心控制器基类**: [classes/controller/Controller.php](mdc:classes/controller/Controller.php)
  - 定义控制器通用生命周期 `init → setMedia → postProcess → initHeader → initContent → initFooter → display`（由 `run()` 调度）。
  - 资产老接口：`addCSS`/`addJS`；多数 1.7+ 主题用新接口见 FrontController。

- **前台控制器核心实现**: [classes/controller/FrontController.php](mdc:classes/controller/FrontController.php)
  - 扩展并实现前台页面初始化、模板变量注入、资产注册、重定向、地理定位与维护模式等。
  - 关键方法：
    - `init()`：SSL 重定向、购物车/货币/客户初始化、canonical 重定向。
    - `assignGeneralPurposeVariables()`：集中向 Smarty 注入全局变量（购物车、站点、客户、配置、像素/埋点等）。
    - `setMedia()`：通过 `registerStylesheet`/`registerJavascript` 注册 CSS/JS（含移动端/RTL/缓存版本号）。
    - `getTemplateVar*()`：封装模板用 URL、Shop、配置、货币、页面信息等。

- **Profiling 包装控制器**: [tools/profiling/Controller.php](mdc:tools/profiling/Controller.php)
  - 声明 `abstract class Controller extends ControllerCore`，在 `run()` 过程中埋点计时并注入性能面板。
  - 若启用 profiling，则 FrontController 继承的是此包装类。

- **配置与服务容器**:
  - 全局配置目录: [config/](mdc:config)（`bootstrap.php`、`services/` 等）。
  - 依赖容器：`ControllerCore::buildContainer()` 返回前台/后台对应容器。

- **主题与资源**:
  - 主题路径由 shop theme 提供；静态资源通过 `StylesheetManager/JavascriptManager` 管理与合并（CCC）。
  - 相关配置键：`PS_CSS_THEME_CACHE`、`PS_JS_THEME_CACHE`、`PS_CANONICAL_REDIRECT`、`PS_SSL_ENABLED(_EVERYWHERE)`。

### 常见改动落点

- **新增全局模板变量**: 修改 [classes/controller/FrontController.php](mdc:classes/controller/FrontController.php) 的 `assignGeneralPurposeVariables()`。
- **新增/替换页面级静态资源**: 修改 `setMedia()` 并用 `registerStylesheet`/`registerJavascript`（避免直接用老 `addCSS/JS`）。
- **注入页面特定元信息/Body 类**: 修改 `getTemplateVarPage()`。
- **统一页面 URL 与路由**: 修改 `getTemplateVarUrls()`（含常用 page 链接表）与 `getPageName()` 的判定逻辑。
- **调试生命周期**: 若启用 profiling，参考 [tools/profiling/Controller.php](mdc:tools/profiling/Controller.php) 输出的性能面板。

### 注意事项

- 该项目对核心 `FrontControllerCore` 有定制，请谨慎合并上游或升级；优先在现有扩展点（Hook/变量注入/资产注册）上改动。
- 涉及 Cookie/Session/重定向的改动需评估 `canonicalRedirection()`、`sslRedirection()` 与 `prepareNotifications()` 的影响。
- 资产合并/版本号会影响缓存失效策略，变更样式/脚本时注意 `version` 参数与主题缓存配置。


