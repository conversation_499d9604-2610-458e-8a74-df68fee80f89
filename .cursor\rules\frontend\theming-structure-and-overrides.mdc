---
description: 
globs: themes/ztheme/**/*.html,themes/ztheme/**/*tpl
alwaysApply: false
---
### 主题结构与模板覆盖

- **主题目录**: [themes/](mdc:themes)
  - 存放全站模板、样式与脚本；根据当前 Shop 所选主题决定解析路径。
  - 支持 RTL/移动端资源按需加载（由 `FrontController::setMedia()` 与资源管理器处理）。

- **模板解析优先级（简化）**
  1. 主题对模块模板的覆盖（`themes/<theme>/modules/<module>/views/templates/...`）
  2. 模块自身模板（`modules/<module>/views/templates/...`）
  3. 核心/控制器默认模板（若存在）

- **静态资源（CSS/JS）**
  - 前台：统一在控制器 `setMedia()` 中用 `registerStylesheet`/`registerJavascript` 注册。
  - 合并与缓存：`PS_CSS_THEME_CACHE`、`PS_JS_THEME_CACHE` 控制；必要时通过 `version` 参数触发失效。

- **常见操作**
  - 覆盖模块模板：在主题下按相同相对路径放置 smarty 模板文件。
  - 新增主题级别全局样式/脚本：在主题入口模板/布局中或通过控制器 `setMedia()` 注册。
  - 局部页面差异化资源：在对应页面控制器里基于条件注册、或通过前台相关 Hook 注入。

- **关联文件**
  - 控制器生命周期与变量： [classes/controller/FrontController.php](mdc:classes/controller/FrontController.php)
  - 生命周期总览与改动落点： [classes/controller/Controller.php](mdc:classes/controller/Controller.php)

  - Profiling 与生命周期调试： [tools/profiling/Controller.php](mdc:tools/profiling/Controller.php)