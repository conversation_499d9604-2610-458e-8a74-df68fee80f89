<?php

/**
 * 3SD Secure 流程回调
 */

use Illuminate\Support\Str;

include_once __DIR__ . '/../../classes/WallexCurlPost.php';

class WallexReturnModuleFrontController extends ModuleFrontController
{
    public function postProcess()
    {
        //支付意向ID
        $payment_intent_id = Tools::getValue('intent_id');
        if (!$payment_intent_id) {
            echo 'error intent id';
            exit;
        }

        //支付类型
        $payment_method = Tools::getValue('payment');
        if (!$payment_method) {
            echo 'error payment';
            exit;
        }

        switch ($payment_method) {
            case 'card':
                $this->card($payment_intent_id);
                break;
            case 'klarna':
                $this->klarna($payment_intent_id);
                break;
            case 'apple':
                $this->apple($payment_intent_id);
                break;
            case 'google':
                $this->google($payment_intent_id);
                break;
            case 'afterpay':
                $this->afterpay($payment_intent_id);
                break;
        }
    }

    private function card($payment_intent_id)
    {
        //订单
        $order_id = Tools::getValue('order_id');
        $order_reference = base64_decode($order_id);
        $sql = "SELECT id_order FROM " . _DB_PREFIX_ . "orders WHERE reference = '" . pSQL($order_reference) . "'";
        $id_order = Db::getInstance()->getValue($sql);
        if (!$id_order) {
            //重定向
            Tools::redirect($this->context->link->getPageLink('order', true, null));
            exit;
        }
        $order = new Order((int)$id_order);

        //客户信息
        $customer = new Customer($order->id_customer);

        $three_ds_response = [];
        //3DS 第一步 继续确认订单
        $threeDSMethodData = Tools::getValue('threeDSMethodData');
        // 首次确认
        if ($threeDSMethodData) {
            $three_ds_response = $this->threeDSConfirmContinueOne($payment_intent_id, $threeDSMethodData, $order, $customer);
            $this->responseCheck($three_ds_response, $order, $customer, $payment_intent_id);
        }

        //3DS 第二步 继续确认订单
        $crse = Tools::getValue('cres');
        $pares = Tools::getValue('PaRes');

        if ($crse || $pares) {
            // 二次确认
            $three_ds_response = $this->threeDSConfirmContinueOne($payment_intent_id, $crse, $order, $customer);;
            $retrieve_order_response = (new WallexCurlPost())->_retrieveOrder($payment_intent_id, '', '', $order->reference);
            //            $acs_response = $crse ? ('cres=' . $crse) : 'PaRes=' . $pares;
            //            $three_ds_response = $this->threeDSConfirmContinueTwo($payment_intent_id, $acs_response, $order, $customer);
            $this->responseCheck($retrieve_order_response, $order, $customer, $payment_intent_id);
        }

        //支付失败
        $order->setCurrentState(_PS_OS_ERROR_);
        //重定向

        // 失败跳转
        Tools::redirect($this->context->link->getPageLink(
            'order-error',
            true,
            (int)$this->context->language->id,
            [
                'id_cart' => (int)$order->id_cart,
                'id_module' => (int)$this->module->id,
                'id_order' => (int)$order->id,
                'key' => $customer->secure_key,
                'payment' => 'Credit or Debit Card',
            ]
        ));
        $message = $this->module->getTranslator()->trans('Payment Failed', [], 'Modules.Wallexapi.Shop') . (isset($three_ds_response['message']) ? "," . $three_ds_response['message'] : '') . ".";
        Tools::redirect($this->context->link->getPageLink('order', true, null, ['error_message' => $message]));
        exit;
    }

    public function responseCheck($confirm_order_response, $order, $customer, $payment_intent_id)
    {
        //支付状态
        $confirm_status = $confirm_order_response['status'] ?? '';

        //支付失败
        if (!$confirm_status) {
            $order->setCurrentState(_PS_OS_ERROR_);
        }

        switch ($confirm_status) {
            case 'SUCCEEDED':

                //保存交易号和卡后四位
                $last4 = '1212';
                if (
                    isset($confirm_order_response['latest_payment_attempt'])
                    && isset($confirm_order_response['latest_payment_attempt']['payment_method'])
                    && isset($confirm_order_response['latest_payment_attempt']['payment_method']['card'])
                    && isset($confirm_order_response['latest_payment_attempt']['payment_method']['card']['last4'])
                ) {
                    $last4 = $confirm_order_response['latest_payment_attempt']['payment_method']['card']['last4'];
                }
                Db::getInstance()->execute("update ps_order_payment set transaction_id='" . pSQL($payment_intent_id) . "', card_number='" . pSQL($last4) . "' where order_reference='" . pSQL($order->reference) . "'");

                //支付成功
                $order->setCurrentState(_PS_OS_PAYMENT_);

                //跳转支付成功页面
                Tools::redirect($this->context->link->getPageLink(
                    'order-confirmation',
                    true,
                    (int)$this->context->language->id,
                    [
                        'id_cart' => (int)$order->id_cart,
                        'id_module' => (int)$this->module->id,
                        'id_order' => (int)$order->id,
                        'key' => $customer->secure_key,
                    ]
                ));
                exit;
            case 'REQUIRES_CUSTOMER_ACTION':
                /**
                 * 待支付，需要用户确认
                 * 开始走3D Secure流程
                 * 第二步提交
                 */
                $next_action = $confirm_order_response['next_action'] ?? '';
                if ($next_action) {
                    $this->context->smarty->assign([
                        'url' => $next_action['url'],
                        'data' => \Illuminate\Support\Arr::get($next_action, 'data', []),
                    ]);
                }
                echo $this->context->smarty->fetch('module:wallex/views/templates/front/three_ds_collect_device.tpl');
                exit;
        }
    }

    /**
     * 确认Klarna支付请求
     * @param mixed $payment_intent_id
     * @param mixed $threeDSMethodData
     * @param array $order
     * @param array $customer
     * @return mixed
     */
    private function threeDSConfirmContinueOne($payment_intent_id, $threeDSMethodData, $order, $customer)
    {
        //请求信息
        $request_data = [
            'request_id' => WallexCurlPost::generateRequestId(WallexConfig::CONFIRM_ORDER, $order->reference),
            'type' => '3ds_continue',
            'three_ds' => [
                "acs_response" => $threeDSMethodData
            ]
        ];
        //确认支付请求
        return (new WallexCurlPost())->_confirmContinue($payment_intent_id, $request_data, $order->reference);
        // return (new WallexKlarnaCurlPost())->_confirmOrder($payment_intent_id, $request_data);
    }


    private function klarna($payment_intent_id)
    {
        //订单
        $order_id = Tools::getValue('order_id');
        $order_reference = base64_decode($order_id);
        $sql = "SELECT id_order FROM " . _DB_PREFIX_ . "orders WHERE reference = '" . pSQL($order_reference) . "'";
        $id_order = Db::getInstance()->getValue($sql);
        if (!$id_order) {
            //重定向
            Tools::redirect($this->context->link->getPageLink('order', true, null));
            exit;
        }
        $order = new Order((int)$id_order);

        //客户信息
        $customer = new Customer($order->id_customer);

        //klarna需要手动捕获支付情况
        $this->captureOrder($payment_intent_id, $order, $customer, Tools::getValue('payment'));
    }

    //afterpay确认支付
    private function afterpay($payment_intent_id)
    {
        //订单
        $order_id = Tools::getValue('order_id');
        $order_reference = base64_decode($order_id);
        $sql = "SELECT id_order FROM " . _DB_PREFIX_ . "orders WHERE reference = '" . pSQL($order_reference) . "'";
        $id_order = Db::getInstance()->getValue($sql);
        if (!$id_order) {
            //重定向
            Tools::redirect($this->context->link->getPageLink('order', true, null));
            exit;
        }
        $order = new Order((int)$id_order);

        //客户信息
        $customer = new Customer($order->id_customer);

        //afterpay需要手动捕获支付情况
        $this->captureOrder($payment_intent_id, $order, $customer, Tools::getValue('payment'));
    }

    private function apple($payment_intent_id)
    {
        $this->common($payment_intent_id);
    }

    private function google($payment_intent_id)
    {
        $this->common($payment_intent_id);
    }

    private function common($payment_intent_id)
    {
        //订单
        $cart_id = Tools::getValue('cart_id');
        $cart_id = base64_decode($cart_id);

        //检索支付状态
        $retrieve_order_response = (new WallexCurlPost())->_retrieveOrder($payment_intent_id, '', '', Tools::getValue('cart_id'));

        //支付状态
        $retrieve_status = $retrieve_order_response['status'] ?? '';

        //支付成功
        if ($retrieve_status == 'SUCCEEDED') {

            //判断订单是否生成
            $order_id = Order::getOrderByCartId($cart_id);
            if ($order_id) {
                $order = new Order($order_id);
                //将pending和failed的订单改为成功
                if (in_array($order->current_state, [_PS_OS_CHEQUE_, _PS_OS_ERROR_])) {
                    //保存交易号和卡后四位
                    $last4 = '1212';
                    if (
                        isset($retrieve_order_response['latest_payment_attempt'])
                        && isset($retrieve_order_response['latest_payment_attempt']['payment_method'])
                        && isset($retrieve_order_response['latest_payment_attempt']['payment_method']['card'])
                        && isset($retrieve_order_response['latest_payment_attempt']['payment_method']['card']['last4'])
                    ) {
                        $last4 = $retrieve_order_response['latest_payment_attempt']['payment_method']['card']['last4'];
                    }
                    Db::getInstance()->execute("update ps_order_payment set transaction_id='" . pSQL($payment_intent_id) . "', card_number='" . pSQL($last4) . "' where order_reference='" . pSQL($order->reference) . "'");
                }
                $order->setCurrentState(_PS_OS_PAYMENT_);
            } else {
                $cart = new Cart($cart_id);
                if (Validate::isLoadedObject($cart)) {
                    $customer = new Customer($cart->id_customer);
                    //验证客户是否可用
                    if (Validate::isLoadedObject($customer)) {
                        //未生成订单的，直接生成一个成功的订单
                        $this->module->validateOrder(
                            (int)$cart_id,
                            _PS_OS_PAYMENT_,    //直接生成已支付订单
                            (float)$cart->getOrderTotal(true, Cart::BOTH),
                            // $this->module->displayName,
                            Tools::getValue('payment'), //精准支付方式
                            null,
                            null,
                            (int)$cart->id_currency,
                            false,
                            $customer->secure_key
                        );
                        $order_id = (int)$this->module->currentOrder;
                        $order = new Order($order_id);
                        //保存交易号和卡后四位
                        $last4 = '1212';
                        if (
                            isset($retrieve_order_response['latest_payment_attempt'])
                            && isset($retrieve_order_response['latest_payment_attempt']['payment_method'])
                            && isset($retrieve_order_response['latest_payment_attempt']['payment_method']['card'])
                            && isset($retrieve_order_response['latest_payment_attempt']['payment_method']['card']['last4'])
                        ) {
                            $last4 = $retrieve_order_response['latest_payment_attempt']['payment_method']['card']['last4'];
                        }
                        Db::getInstance()->execute("update ps_order_payment set transaction_id='" . pSQL($payment_intent_id) . "', card_number='" . pSQL($last4) . "' where order_reference='" . pSQL($order->reference) . "'");
                    }
                }
            }
        }

        echo 'success';
        exit;
    }

    /**
     * 捕获支付
     * @param mixed $payment_intent_id
     * @param mixed $acs_response
     * @param mixed $order
     * @param mixed $customer
     * @return array
     */
    public function captureOrder($payment_intent_id, $order, $customer, $payment_method)
    {
        //请求信息
        $request_data = [
            // 'request_id' => WallexKlarnaCurlPost::generateRequestId(WallexKlarnaConfig::CAPTURE_ORDER, $payment_intent_id),
            'request_id' => WallexCurlPost::generateRequestId(WallexConfig::CAPTURE_ORDER, $payment_intent_id),
            'amount' => $order->total_paid,
        ];

        //发起请求
        $capture_response = (new WallexCurlPost())->_retrieveOrder($payment_intent_id, $request_data, $payment_method, $order->reference);

        //支付状态
        $confirm_status = $capture_response['status'] ?? '';

        //支付成功
        if ($confirm_status == 'SUCCEEDED') {
            //保存交易号和卡后四位
            $last4 = '1212';
            if (
                isset($capture_response['latest_payment_attempt'])
                && isset($capture_response['latest_payment_attempt']['payment_method'])
                && isset($capture_response['latest_payment_attempt']['payment_method']['card'])
                && isset($capture_response['latest_payment_attempt']['payment_method']['card']['last4'])
            ) {
                $last4 = $capture_response['latest_payment_attempt']['payment_method']['card']['last4'];
            }
            Db::getInstance()->execute("update ps_order_payment set transaction_id='" . pSQL($payment_intent_id) . "', card_number='" . pSQL($last4) . "' where order_reference='" . pSQL($order->reference) . "'");
            $order->setCurrentState(_PS_OS_PAYMENT_);

            //跳转支付成功页面
            Tools::redirect($this->context->link->getPageLink(
                'order-confirmation',
                true,
                (int)$this->context->language->id,
                [
                    'id_cart' => (int)$order->id_cart,
                    'id_module' => (int)$this->module->id,
                    'id_order' => (int)$order->id,
                    'key' => $customer->secure_key,
                ]
            ));
            exit;
        } elseif ($confirm_status === 'REQUIRES_CUSTOMER_ACTION' && isset($capture_response['next_action']['url'])) {
            //用户进一步确认操作
            Tools::redirect($capture_response['next_action']['url']);
            exit;
        } elseif ($confirm_status === 'REQUIRES_CAPTURE') {
            //再次捕获结果 afterpay klarna
            //发起请求
            $capture_response2 = (new WallexCurlPost())->_captureOrder($payment_intent_id, $request_data, $payment_method);
            //支付状态
            $confirm_status2 = $capture_response2['status'] ?? '';

            //支付成功
            if ($confirm_status2 == 'SUCCEEDED') {
                
                //保存交易号和卡后四位
                $last4 = '1212';
                if (
                    isset($capture_response2['latest_payment_attempt'])
                    && isset($capture_response2['latest_payment_attempt']['payment_method'])
                    && isset($capture_response2['latest_payment_attempt']['payment_method']['card'])
                    && isset($capture_response2['latest_payment_attempt']['payment_method']['card']['last4'])
                ) {
                    $last4 = $capture_response2['latest_payment_attempt']['payment_method']['card']['last4'];
                }
                Db::getInstance()->execute("update ps_order_payment set transaction_id='" . pSQL($payment_intent_id) . "', card_number='" . pSQL($last4) . "' where order_reference='" . pSQL($order->reference) . "'");

                $order->setCurrentState(_PS_OS_PAYMENT_);
                //跳转支付成功页面
                Tools::redirect($this->context->link->getPageLink(
                    'order-confirmation',
                    true,
                    (int)$this->context->language->id,
                    [
                        'id_cart' => (int)$order->id_cart,
                        'id_module' => (int)$this->module->id,
                        'id_order' => (int)$order->id,
                        'key' => $customer->secure_key,
                    ]
                ));
                exit;
            }
        }

        //支付失败
        $order->setCurrentState(_PS_OS_ERROR_);
        // 失败跳转
        Tools::redirect($this->context->link->getPageLink(
            'order-error',
            true,
            (int)$this->context->language->id,
            [
                'id_cart' => (int)$order->id_cart,
                'id_module' => (int)$this->module->id,
                'id_order' => (int)$order->id,
                'key' => $customer->secure_key,
                'payment' => 'Credit or Debit Card',
            ]
        ));
        //重定向
        $message = $this->module->getTranslator()->trans('Payment Failed', [], 'Modules.Wallexklarna.Shop') . (isset($capture_response['message']) ? "," . $capture_response['message'] : '') . ".";
        Tools::redirect($this->context->link->getPageLink('order', true, null, ['error_message' => $message]));
        exit;
    }
}
