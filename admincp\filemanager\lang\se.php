<?php
/*
* Important - this file MUST implement all strings defined in base en.php file
*/
define('lang_Select', 'Välj'); // Select
define('lang_Erase', 'Radera'); // Erase
define('lang_Open', 'Ö<PERSON>na'); // Open
define('lang_Confirm_del', 'Är du säker på att du vill radera denna fil?'); //Are you sure you want to delete this file?
define('lang_All', 'Alla'); // All
define('lang_Files', 'Filer'); // Files
define('lang_Images', 'Bilder'); // Images
define('lang_Archives', 'Arkiv'); // Archives
define('lang_Error_Upload', 'Den uppladdade filen överskrider max storleken.'); // The uploaded file exceeds the max size allowed.
define('lang_Error_extension', 'Filtypen är ej tillåten.'); // File extension is not allowed.
define('lang_Upload_file', 'Ladda upp'); // Upload
define('lang_Filters', 'Filter'); // Filters
define('lang_Videos', 'Videor'); // Videos
define('lang_Music', 'Musik'); // Music
define('lang_New_Folder', 'Ny katalog'); // New Folder
define('lang_Folder_Created', 'Katalogen har skapats'); // Folder correctly created
define('lang_Existing_Folder', 'Befintlig katalog'); // Existing folder
define('lang_Confirm_Folder_del', 'Är du säker på att du vill radera denna katalog samt dess innehåll?'); // Are you sure to delete the folder and all the elements in it?
define('lang_Return_Files_List', 'Tillbaka till filvisaren'); // Return to files list
define('lang_Preview', 'Förhandsgranska'); // Preview
define('lang_Download', 'Ladda hem'); // Download
define('lang_Insert_Folder_Name', 'Ange katalog namn:'); // Insert folder name:
define('lang_Root', 'root'); // root
define('lang_Rename', 'Byt namn'); // Rename
define('lang_Back', 'tillbaka'); // back
define('lang_View', 'Visa'); // View
define('lang_View_list', 'Listvy'); // List view
define('lang_View_columns_list', 'Columnvy'); // Columns list view
define('lang_View_boxes', 'Boxvy'); // Box view
define('lang_Toolbar', 'Verktygsfält'); // Toolbar
define('lang_Actions', 'Åtgärder'); // Actions
define('lang_Rename_existing_file', 'Det finns redan en fil med det namnet'); // The file is already existing
define('lang_Rename_existing_folder', 'Det finns redan en katalog med det namnet'); // The folder is already existing
define('lang_Empty_name', 'Du har ej angivet något namn'); // The name is empty
define('lang_Text_filter', 'text filter'); // text filter
define('lang_Swipe_help', 'Svep över filnamnet/katalognamnet för att visa åtgärder'); // Swipe the name of file/folder to show options
define('lang_Upload_base', 'Basal uppladdning'); // Base upload
define('lang_Upload_java', 'JAVA uppladdning (för stora filer)'); // JAVA upload (big size files)
define('lang_Upload_java_help', "Om Java Appleten inte laddar, 1. säkerställ att Java är installerat, <a href='http://java.com/en/download/'>ladda hem</a> och installera om det saknas  2. säkerställ att programmet inte blokeras av din brandvägg"); // If the Java Applet doesn't load, 1. make sure you have Java installed, otherwise <a href='http://java.com/en/download/'>[download link]</a>   2. make sure nothing is blocked by your firewall
define('lang_Upload_base_help', "Dra och släpa filer eller klicka ovan och välj en eller flera filer. När uppladningen är klar, klicka på 'Tillbaka till filvisaren' knappen."); // Drag & Drop files or click in the area above (modern browsers) and select the file(s). When the upload is complete, click the 'Return to files list' button.
define('lang_Type_dir', 'katalog'); // dir
define('lang_Type', 'Typ'); // Type
define('lang_Dimension', 'Dimension'); // Dimension
define('lang_Size', 'Storlek'); // Size
define('lang_Date', 'Datum'); // Date
define('lang_Filename', 'Filname'); // Filename
define('lang_Operations', 'Handlingar'); // Operations
define('lang_Date_type', 'y-m-d'); // y-m-d
define('lang_OK', 'OK'); // OK
define('lang_Cancel', 'Avbryt'); // Cancel
define('lang_Sorting', 'sortering'); // sorting
define('lang_Show_url', 'visa sökväg'); // show URL
define('lang_Extract', 'packa upp här'); // extract here
define('lang_File_info', 'fil information'); // file info
define('lang_Edit_image', 'editera bild'); // edit image
define('lang_Duplicate', 'Duplicera'); // Duplicate
