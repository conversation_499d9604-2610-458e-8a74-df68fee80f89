<?php
/**
 * 前端点击提交处理的地方
 */
include_once __DIR__ . '/../../classes/WallexCurlPost.php';
include_once __DIR__ . '/../../classes/WallexConfig.php';

class WallexValidationModuleFrontController extends ModuleFrontController
{
	/**
	 * @var PaymentModule
	 */
	public $module;

	protected $payment = [];

	/**
	 * 验证并生成系统订单
	 * @return OrderCore
	 */
	private function validateOrder()
	{
		//验证上下文和支付方式是否可用
		if (false === $this->checkIfContextIsValid() || false === $this->checkIfPaymentOptionIsAvailable()) {
			Tools::redirect($this->context->link->getPageLink(
				'order',
				true,
				(int) $this->context->language->id,
				[
					'step' => 1,
				]
			));
		}

		$customer = new Customer($this->context->cart->id_customer);

		//验证客户是否可用
		if (false === Validate::isLoadedObject($customer)) {
			Tools::redirect($this->context->link->getPageLink(
				'order',
				true,
				(int) $this->context->language->id,
				[
					'step' => 1,
				]
			));
		}

		//创建订单 - 直接使用支付成功状态，确保创建支付记录
		$this->module->validateOrder(
			(int) $this->context->cart->id,
			_PS_OS_PAYMENT_,	//直接使用支付成功状态，确保创建 order_payment 记录
			(float) $this->context->cart->getOrderTotal(true, Cart::BOTH),
			// $this->module->displayName,
			Tools::getValue('payment'), //精准支付方式
			null,
			null,
			(int) $this->context->currency->id,
			false,
			$customer->secure_key
		);

		$this->context->cookie->__set('id_cart', $this->context->cart->id);
		$this->context->cookie->__set('cid', $this->context->cart->id);

		$order_id = (int) $this->module->currentOrder;
		return new Order($order_id);
	}

	/**
	 * 创建支付请求
	 * @param OrderCore $order
	 * @param array $billing_info
	 * @param array $shipping_info
	 * @return mixed
	 */
	private function createOrder($order, $billing_info, $shipping_info,$payment_method = '')
	{
		//请求信息
		$request_data = [];
		//请求ID - 唯一
		// $request_data['request_id'] = WallexCurlPost::generateRequestId(WallexConfig::CREATE_ORDER, $order->reference);
		$request_id = WallexCurlPost::generateRequestId(WallexConfig::CREATE_ORDER, $order->reference);
		$request_id = substr($request_id, 0, 64);
		$request_data['request_id'] = $request_id;
		//支付金额
		$request_data['amount'] = $order->total_paid;
        // 3ds
//		$request_data['payment_method_options'] =  [
//            "card" => [
//				"auto_capture"  => true,	//自动确认
//                "three_ds_action" => "FORCE_3DS"
//            ]
//        ];
		//货币
		$currency = new Currency((int) $order->id_currency);
		$request_data['currency'] = $currency->iso_code;
		//商户唯一单号
		$request_data['merchant_order_id'] = WallexConfig::SITE_PREFIX . '-' . $order->reference;
		//回调地址
		$request_data['return_url'] = $this->context->link->getModuleLink(
			$this->module->name,
			'return'
		);
		//客户信息
		$request_data['customer'] = [
			'first_name' => $billing_info['firstname'],
			'last_name' => $billing_info['lastname'],
			'email' => $billing_info['email'],
			'phone_number' => $billing_info['phone']
		];
		//产品信息
		$products = [];
		foreach ($order->getOrderDetailList() as $item) {
			$product = new Product($item['product_id'], false, $order->id_lang);
			$url = $this->context->link->getProductLink($product);
			$products[] = [
				'code' => $product->supplier_reference,
				'desc' => $item['product_name'] . ' is good!',
				'name' => $item['product_name'],
				'quantity' => $item['product_quantity'],
				'sku' => $product->supplier_reference,
				'type' => 'physical',
				'unit_price' => $item['product_price'],
				'url' => $url
			];
		}
		//配送信息
		$shipping = [
			"address" =>
				[
					"city" => $shipping_info['city'],
					'state' => $shipping_info['state_iso_code'],
					"country_code" => $shipping_info['country_iso_code'],
					"postcode" => $shipping_info['postcode'],
					"street" => $shipping_info['street']
				],
			"fee_amount" => $shipping_info['shipping_fee'],	//运费
			"email" => $shipping_info['email'],
			"first_name" => $shipping_info['firstname'],
			"last_name" => $shipping_info['lastname'],
			"phone_number" => $shipping_info['phone']
		];
		//订单信息
		$request_data['order'] = [
			'products' => $products,
			'shipping' => $shipping
		];
		//创建支付请求
		return (new WallexCurlPost())->_createOrder($request_data,$order->reference,$payment_method);
	}

	/**
	 * 确认信用卡支付请求
	 * @param mixed $payment_intent_id
	 * @param mixed $order_reference
	 * @param array $billing_info
	 * @param array $payment_info
	 * @return mixed
	 */
	private function confirmCardOrder($payment_intent_id, $order_reference, $billing_info, $payment_info)
	{
		//请求信息
		$request_data = [];
		//请求ID - 唯一
		// $request_data['request_id'] = WallexCurlPost::generateRequestId(WallexConfig::CONFIRM_ORDER, $order_reference);
		$request_id = WallexCurlPost::generateRequestId(WallexConfig::CREATE_ORDER, $order_reference);
		$request_id = substr($request_id, 0, 64);
		$request_data['request_id'] = $request_id;
		//更新回调地址，用于3DS
		$request_data['return_url'] = $this->context->link->getModuleLink(
			$this->module->name,
			'return',
			[
				'intent_id' => $payment_intent_id,
				'order_id' => base64_encode($order_reference),
				'payment' => 'card',
			],
			true
		);
		//支付信息
		$request_data['payment_method'] = [
			"type" => "card",
			"card" => [
				"billing" => [
					"address" => [
						"state" => $billing_info['state_iso_code'],
						"city" => $billing_info['city'],
						"country_code" => $billing_info['country_iso_code'],
						"postcode" => $billing_info['postcode'],
						"street" => $billing_info['street']
					],
					"phone_number" => $billing_info['phone'],//2
					"email" => $billing_info['email'],
					"first_name" => $billing_info['firstname'],
					"last_name" => $billing_info['lastname']
				],
				"cvc" => $payment_info["cc_cid"],
				"expiry_month" => $payment_info["cc_exp_month"],
				"expiry_year" => $payment_info["cc_exp_year"],
				"number" => $payment_info["cc_number"]
			]
		];
		//支付选项
		$request_data['payment_method_options'] = [
			"card" => [
				"auto_capture"  => true,	//自动确认
//                "three_ds_action" => "FORCE_3DS"
			]
		];
		//设备信息
		$request_data['device_data'] = [
			"device_id" => $payment_info['cc_ss_issue'],
			"ip_address" => $this->getIP()
		];
		//确认支付请求
		return (new WallexCurlPost())->_confirmOrder($payment_intent_id, $request_data,$order_reference);
	}

	/**
	 * 确认Klarna支付请求
	 * @param mixed $payment_intent_id
	 * @param mixed $order_reference
	 * @param array $billing_info
	 * @param array $payment_info
	 * @return mixed
	 */
	private function confirmKlarnaOrder($payment_intent_id, $order_reference, $billing_info)
	{
		$request_id = WallexCurlPost::generateRequestId(WallexConfig::CREATE_ORDER, $order_reference);
		$request_id = substr($request_id, 0, 64);
		//请求信息
		$request_data = [
			// 'request_id' => WallexKlarnaCurlPost::generateRequestId(WallexKlarnaConfig::CONFIRM_ORDER, $order_reference),
			'request_id' => WallexCurlPost::generateRequestId(WallexConfig::CONFIRM_ORDER, $order_reference),
			// 'request_id' => $request_id,
			'payment_method' => [
				"type" => "klarna",
				"klarna" => [
					"country_code" => $billing_info['country_iso_code'],
					"language" => $this->context->language->iso_code,
					"billing" => [
						"address" => [
							"state" => $billing_info['state_iso_code'],
							"city" => $billing_info['city'],
							"country_code" => $billing_info['country_iso_code'],
							"postcode" => $billing_info['postcode'],
							"street" => $billing_info['street']
						],
						"phone_number" => $billing_info['phone'],//2
						"email" => $billing_info['email'],
						"first_name" => $billing_info['firstname'],
						"last_name" => $billing_info['lastname']
					]
				]
			],
			'payment_method_options' => [
				"klarna" => [
					"auto_capture" => false	//自动确认
				]
			],
			'return_url' => $this->context->link->getModuleLink(
				$this->module->name,
				'return',
				[
					'intent_id' => $payment_intent_id,
					'order_id' => base64_encode($order_reference),
					'payment' => 'klarna',
				],
				true
			),
			'device_data' => [
				"ip_address" => $this->getIP()
			]
		];
		//确认支付请求
		return (new WallexCurlPost())->_confirmOrder($payment_intent_id, $request_data);
		// return (new WallexKlarnaCurlPost())->_confirmOrder($payment_intent_id, $request_data);
	}

	/**
	 * 确认afterpay支付请求
	 * @param mixed $payment_intent_id
	 * @param mixed $order_reference
	 * @param array $billing_info
	 * @param array $payment_info
	 * @return mixed
	 */
	public function confirmAfterpayOrder($payment_intent_id, $order_reference, $billing_info){
		//请求信息
		$request_data = [
			'request_id' => WallexCurlPost::generateRequestId(WallexConfig::CONFIRM_ORDER, $order_reference),
			'payment_method' => [
				"type" => "afterpay",
				"afterpay" => [
					"shopper_email" => $billing_info['email'],
					"billing" => [
						"address" => [
							"state" => $billing_info['state_iso_code'],
							"city" => $billing_info['city'],
							"country_code" => $billing_info['country_iso_code'],
							"postcode" => $billing_info['postcode'],
							"street" => $billing_info['street']
						],
						"phone_number" => $billing_info['phone'],//2
						"email" => $billing_info['email'],
						"first_name" => $billing_info['firstname'],
						"last_name" => $billing_info['lastname']
					]
				]
			],
			'return_url' => $this->context->link->getModuleLink(
				$this->module->name,
				'return',
				[
					'intent_id' => $payment_intent_id,
					'order_id' => base64_encode($order_reference),
					'payment' => 'afterpay',
				],
				true
			),
			'device_data' => [
				"ip_address" => $this->getIP()
			]
		];
		//确认支付请求
		return (new WallexCurlPost())->_confirmOrder($payment_intent_id, $request_data,$order_reference,'afterpay');
	}

	/**
	 * {@inheritdoc}
	 */
	public function postProcess()
	{
		//生成系统订单
		$order = $this->validateOrder();

		//支付方式不对
		$payment_method = Tools::getValue('payment');

		/**
		 * 请求信息整理
		 */

		//客户信息
		$customer = new Customer($this->context->cart->id_customer);
		$email = $customer->email;

		//账单地址
		$billingAddressId = $order->id_address_invoice;
		$billingAddress = new Address((int) $billingAddressId);
		//省
		$billing_state_iso_code = '';
		if ($billingAddress->id_state) {
			$billing_state = new State((int) $billingAddress->id_state);
			$billing_state_iso_code = $billing_state->iso_code;
		}
		//国家
		$billing_country = new Country((int) $billingAddress->id_country);
		//账单信息
		$billing_info = [
			'state_iso_code' => $billing_state_iso_code,
			'city' => $billingAddress->city,
			'country_iso_code' => $billing_country->iso_code,
			'postcode' => $billingAddress->postcode,
			'street' => $billingAddress->address1 . ' ' . $billingAddress->address2,
			'phone' => $billingAddress->phone,
			'email' => $email,
			'firstname' => $billingAddress->firstname,
			'lastname' => $billingAddress->lastname,
		];

		//配送地址
		$shippingAddressId = $order->id_address_delivery;
		$shippingAddress = new Address((int) $shippingAddressId);
		//省
		$shipping_state_iso_code = '';
		if ($shippingAddress->id_state) {
			$shipping_state = new State((int) $shippingAddress->id_state);
			$shipping_state_iso_code = $shipping_state->iso_code;
		}
		//国家
		$shipping_country = new Country((int) $shippingAddress->id_country);
		//配送信息
		$shipping_info = [
			'state_iso_code' => $shipping_state_iso_code,
			'city' => $shippingAddress->city,
			'country_iso_code' => $shipping_country->iso_code,
			'postcode' => $shippingAddress->postcode,
			'street' => $shippingAddress->address1 . ' ' . $shippingAddress->address2,
			'phone' => $shippingAddress->phone,
			'email' => $email,
			'firstname' => $shippingAddress->firstname,
			'lastname' => $shippingAddress->lastname,
			'shipping_fee' => $order->total_shipping	//运费
		];

		//创建支付请求
		// if($payment_method == 'afterpay'){
			$create_order_response = $this->createOrder($order, $billing_info, $shipping_info,$payment_method);
		// }else{
		// 	$create_order_response = $this->createOrder($order, $billing_info, $shipping_info);
		// }
		
		//发起请求失败
		if ($create_order_response['status'] != 'REQUIRES_PAYMENT_METHOD' || empty($create_order_response['id'])) {
			$order->setCurrentState(_PS_OS_ERROR_);
			//重定向
			Tools::redirect($this->context->link->getPageLink('cart'));
			return false;
		}

		//支付意向ID/交易号
		$payment_intent_id = $create_order_response['id'];
		// 先检查支付记录是否存在，不存在则创建
		$payment_exists = Db::getInstance()->getValue("SELECT COUNT(*) FROM " . _DB_PREFIX_ . "order_payment WHERE order_reference = '" . pSQL($order->reference) . "'");
		if (!$payment_exists) {
			// 创建支付记录
			$order->addOrderPayment(
				$order->total_paid,
				null, // payment_method 会在下面的UPDATE中设置
				$payment_intent_id,
				new Currency($order->id_currency)
			);
		}
		// 更新交易号
		Db::getInstance()->execute("UPDATE " . _DB_PREFIX_ . "order_payment SET transaction_id='" . pSQL($payment_intent_id) . "' WHERE order_reference='" . pSQL($order->reference) . "'");
		error_log("create_order_response:".print_r($create_order_response,true));
		if (!in_array($payment_method, ['card', 'klarna','afterpay'])) {
			//重定向
			Tools::redirect($this->context->link->getPageLink('order'));
			return false;
		}

		//信用卡支付
		if ($payment_method == 'card') {
			//支付卡信息
			$payment_info = [
				'cc_type' => Tools::getValue('cc_type'),
				'cc_owner' => Tools::getValue('cc_owner'),
				'cc_number' => Tools::getValue('cc_number'),
				'cc_cid' => Tools::getValue('cc_cid'),
				'cc_exp_month' => Tools::getValue('cc_exp_month'),
				'cc_exp_year' => Tools::getValue('cc_exp_year'),
				'cc_ss_issue' => Tools::getValue('cc_ss_issue'),
			];

			//发起请求成功 -- 确认支付
			$confirm_order_response = $this->confirmCardOrder($payment_intent_id, $order->reference, $billing_info, $payment_info);
			//支付状态
			$confirm_status = $confirm_order_response['status'] ?? '';

			//支付失败
			if (!$confirm_status) {
				$order->setCurrentState(_PS_OS_ERROR_);
			}

			switch ($confirm_status) {
				case 'SUCCEEDED':
					//支付成功
					$order->setCurrentState(_PS_OS_PAYMENT_);
					//保存交易号和卡后四位
					$last4 = '1212';
					if (isset($confirm_order_response['latest_payment_attempt'])
						&& isset($confirm_order_response['latest_payment_attempt']['payment_method'])
						&& isset($confirm_order_response['latest_payment_attempt']['payment_method']['card'])
						&& isset($confirm_order_response['latest_payment_attempt']['payment_method']['card']['last4'])) {
						$last4 = $confirm_order_response['latest_payment_attempt']['payment_method']['card']['last4'];
					}
					// 先检查支付记录是否存在，不存在则创建
					$payment_exists = Db::getInstance()->getValue("SELECT COUNT(*) FROM " . _DB_PREFIX_ . "order_payment WHERE order_reference = '" . pSQL($order->reference) . "'");
					if (!$payment_exists) {
						// 创建支付记录
						$order->addOrderPayment(
							$order->total_paid,
							null, // payment_method 会在下面的UPDATE中设置
							$payment_intent_id,
							new Currency($order->id_currency)
						);
					}
					// 更新交易号和卡号
					Db::getInstance()->execute("UPDATE " . _DB_PREFIX_ . "order_payment SET transaction_id='" . pSQL($payment_intent_id) . "', card_number='" . pSQL($last4) . "' WHERE order_reference='" . pSQL($order->reference) . "'");
					//跳转支付成功页面
					Tools::redirect($this->context->link->getPageLink(
						'order-confirmation',
						true,
						(int) $this->context->language->id,
						[
							'id_cart' => (int) $order->id_cart,
							'id_module' => (int) $this->module->id,
							'id_order' => (int) $order->id,
							'key' => $customer->secure_key,
						]
					));
					exit;
				case 'REQUIRES_CUSTOMER_ACTION':
					/**
					 * 待支付，需要用户确认
					 * 开始走3D Secure流程
					 * 第一步：用户设备信息收集
					 */
					$order->setCurrentState(_PS_OS_CHEQUE_);
					$next_action = $confirm_order_response['next_action'] ?? '';
					if ($next_action) {
						$this->context->smarty->assign([
							'url' => $next_action['url'],
                            'data' => \Illuminate\Support\Arr::get($next_action,'data',[]),
						]);
					}
					echo $this->context->smarty->fetch('module:wallex/views/templates/front/three_ds_collect_device.tpl');
					exit;
			}
		} elseif ($payment_method == 'afterpay'){
			// 发起请求成功 -- 确认支付
			$confirm_order_response = $this->confirmAfterpayOrder($payment_intent_id,$order->reference,$billing_info);
			//需要跳转到 Afterpay网站支付
			$url = $confirm_order_response['next_action']['url'] ?? '';
			if ($url) {
				Tools::redirect($url);
				exit;
			}
		} else {
			//发起请求成功 -- 确认支付
			$confirm_order_response = $this->confirmKlarnaOrder($payment_intent_id, $order->reference, $billing_info);
			//需要跳转到 Klarna网站支付
			$url = $confirm_order_response['next_action']['url'] ?? '';
			if ($url) {
				Tools::redirect($url);
				exit;
			}
		}


        //跳转支付失败页面
        Tools::redirect($this->context->link->getPageLink(
            'order-error',
            true,
            (int) $this->context->language->id,
            [
                'id_cart' => (int) $order->id_cart,
                'id_module' => (int) $this->module->id,
                'id_order' => (int) $order->id,
                'key' => $customer->secure_key,
                'payment' => 'Credit or Debit Card',
            ]
        ));
		//重定向
		$message = $this->module->getTranslator()->trans('Payment Failed', [], 'Modules.Wallex.Shop') . (isset($confirm_order_response['message']) ? "," . $confirm_order_response['message'] : '') . ".";
		Tools::redirect($this->context->link->getPageLink('order', true, null, ['error_message' => $message]));
		exit;
	}

	/**
	 * Check if the context is valid
	 *
	 * @return bool
	 */
	private function checkIfContextIsValid()
	{
		return true === Validate::isLoadedObject($this->context->cart)
			&& true === Validate::isUnsignedInt($this->context->cart->id_customer)
			&& true === Validate::isUnsignedInt($this->context->cart->id_address_delivery)
			&& true === Validate::isUnsignedInt($this->context->cart->id_address_invoice);
	}

	/**
	 * Check that this payment option is still available in case the customer changed
	 * his address just before the end of the checkout process
	 *
	 * @return bool
	 */
	private function checkIfPaymentOptionIsAvailable()
	{
		$modules = Module::getPaymentModules();
		if (empty($modules)) {
			return false;
		}
		foreach ($modules as $module) {
			if (isset($module['name']) && $this->module->name === $module['name']) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 获取IP
	 * @return string
	 */
	private function getIP()
	{
		if (array_key_exists("HTTP_X_FORWARDED_FOR", $_SERVER))
			$ip = $_SERVER["HTTP_X_FORWARDED_FOR"];
		else if (array_key_exists("HTTP_CLIENT_IP", $_SERVER))
			$ip = $_SERVER["HTTP_CLIENT_IP"];
		else if (array_key_exists("REMOTE_ADDR", $_SERVER))
			$ip = $_SERVER["REMOTE_ADDR"];
		else if (@getenv("HTTP_X_FORWARDED_FOR"))
			$ip = getenv("HTTP_X_FORWARDED_FOR");
		else if (@getenv("HTTP_CLIENT_IP"))
			$ip = getenv("HTTP_CLIENT_IP");
		else if (@getenv("REMOTE_ADDR"))
			$ip = getenv("REMOTE_ADDR");
		else
			$ip = "Unknown";
		$ip = explode(',', $ip);
		return $ip[0];

	}
}
