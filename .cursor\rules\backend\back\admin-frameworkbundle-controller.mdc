---
description: 
globs: src/PrestaShopBundle/Controller/Admin/**/*Controller.php
alwaysApply: false
---
### 后台 Symfony 控制器基类（FrameworkBundleAdminController）

- **位置**: [src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php](mdc:src/PrestaShopBundle/Controller/Admin/FrameworkBundleAdminController.php)
- **基类**: 继承自 Symfony 的 [vendor/symfony/symfony/src/Symfony/Bundle/FrameworkBundle/Controller/AbstractController.php](mdc:vendor/symfony/symfony/src/Symfony/Bundle/FrameworkBundle/Controller/AbstractController.php)
- **用途**: 为后台（BO）控制器提供 PrestaShop 语义的便捷方法（配置、上下文、翻译、Hook 分发、权限判定、分页与排序等）。

### 常用能力
- 配置与上下文
  - `getConfiguration()`（Shop 配置）、`getContext()`（Legacy 上下文）、`getContextLocale()`、`getContextCurrencyIso()`、`getContextLangId()`、`getContextShopId()`
- 翻译与消息
  - `trans()`、`flashErrors()`、`addFlashFormErrors()`、`getFormErrorsForJS()`
- Hook 分发
  - `dispatchHook($hookName, $parameters)`、`renderHook($hookName, $parameters)`
- 权限与导航
  - `authorizationLevel()`、`actionIsAllowed()`、`getForbiddenActionMessage()`、`redirectToDefaultPage()`、`getAdminLink()`、`generateSidebarLink()`
- 呈现与总线
  - `presentGrid()`、`getCommandBus()`、`getQueryBus()`
- 错误与 JSON
  - `returnErrorJsonResponse()`、`getErrorMessageForException()`、`getFallbackErrorMessage()`
- 分页与排序（项目内扩展）
  - `pagination($total, $limit, $page)` 返回总数/页码/范围等
  - `getOrderBy($data, $default_order = '', $suffix = '')` 解析请求中的排序字段并生成 SQL 片段（带可选表前缀）

### 使用建议
- 新建后台控制器时，优先继承该类以复用上下文、翻译、Hook 与权限能力。
- 表格/网格页面：通过 `presentGrid()` 配合 Grid 系统输出；动作权限统一走 `PageVoter` 判定。
- 解析表单错误用于前端校验展示：`getFormErrorsForJS()`；也可用 `addFlashFormErrors()` 快速注入 Flash 错误。
- 若需拼装后台链接，使用 `getAdminLink()`，避免手写 Token 与路由细节。
- 注意注释中的 `@deprecated` 标注与版本差异，迁移时优先使用非弃用方法。

