---
description: 
globs: themes/ztheme/**/*.html,themes/ztheme/**/*tpl
alwaysApply: false
---
### 分类页模板说明（ztheme）

- **模板文件**: [themes/ztheme/templates/catalog/category.tpl](mdc:themes/ztheme/templates/catalog/category.tpl)

- **主要 Smarty 区块**
  - `breadcrumb`：面包屑（当前为空）。
  - `left_column`：左栏筛选/清除筛选、jQuery UI 价格滑块、左侧挂件 Hook。
  - `content`：空（主要内容在 `left_column` 中的右侧容器输出）。
  - `footer`：分类描述、`displayFooterCategory` 钩子与底部模板。
  - `track`：埋点/追踪模板引入（TikTok、分类页追踪）。

- **依赖数据与变量来源**
  - `$layout`：由 `FrontController::getLayout()` 和主题布局解析提供。
  - `$label`、`$category`、`$subcategories`、`$products`：分类控制器与 Presenter 提供。
  - `$orders`：排序选项（包含 `selected` 与 `nextURL`）。
  - `$facets`：由分面搜索模块/控制器提供，含：
    - `left_filters`：左侧通用筛选（radio/checkbox/checkbox_with_image）。
    - `ship_in_48hrs`：快捷发货筛选开关。
    - `color_group_filters`：颜色家族与具体颜色集合，使用 `nextEncodedFacetsURL` 构造链接。
  - `$removes`：当前已选筛选项与清除链接。
  - `$pages`：分页数据（传递至 `_partials/ca-sea-pageination.tpl`）。

- **模板包含与 Hook**
  - 包含：
    - `_partials/add-to-sc-icon.tpl`（收藏/心愿图标）
    - `_partials/ca-sea-pageination.tpl`（分页）
    - `_partials/footer.tpl`（底部）
    - `_partials/tiktok_track.tpl`、`catalog/_partials/category-track.tpl`（埋点）
  - Hook：
    - `displayLeftColumn`（左栏热门/挂件）
    - `displayProductListReviews`（产品列表评分）
    - `displayFooterCategory`（分类底部）

- **前端依赖与交互**
  - 使用 jQuery 与 jQuery UI Slider：`$("#slider-range_45249").slider({...})`
    - 确保页面已加载 jQuery UI（可在控制器 `setMedia()` 里通过 `addJqueryUI('slider')` 或主题资源加载）。
  - 价格区间链接通过 `#facet_45249` 的 `data-slider-*` 属性与 `nextEncodedFacetsURL` 组合生成。
  - 左侧筛选 `facet` 支持三种展示：`price` 滑块、`radio/checkbox`、`checkbox_with_image`（如 Length/图片选项）。
  - 排序下拉：`#category-sort-dropdown-menu`，鼠标移入/移出高亮并保持最后 hover 的项为激活态；滚轮事件用于控制定位与滚动。
  - 图片懒加载：产品主图 `loading="lazy"` 已启用；正反面图按颜色筛选结果或产品图回退。

- **常见改动与落点**
  - 新增筛选类型：扩展 `left_filters` 循环分支（`facet.widgetType` 新值），并在后端模块/控制器提供相应字段与 `nextEncodedFacetsURL`。
  - 调整排序项：修改 `$orders` 的生成逻辑（控制器/模块），模板只读 `label/selected/nextURL`。
  - 修改子分类导航：使用 `$subcategories` 循环，字段需含 `url/name`。
  - 修改产品卡：编辑 `.product-miniature` 结构，注意评分 Hook 与价格/折扣展示兼容性。
  - 颜色族 UI：`$facets.color_group_filters` 中 `attribute_colors` 提供细项；新增族需同步后端构建逻辑。

- **性能与可维护性建议**
  - 尽量将内联 `<style>` 移入主题 CSS（见 `assets/css/theme_pc.css` 等），减少模板内样式。
  - 滑块初始化与 DOM 事件可拆到主题 JS（如 `assets/js/theme.js`），并用 `data-*` 参数驱动。
  - 使用 `nextEncodedFacetsURL` 避免手写查询串，确保和模块生成的 Facets 状态一致。

- **相关后端位置（参考）**
  - 前台控制器初始化与资产： [classes/controller/FrontController.php](mdc:classes/controller/FrontController.php) `setMedia()`、`assignGeneralPurposeVariables()`。
  - Facets/筛选数据来源：通常来自分面搜索模块（如 `ps_facetedsearch`），或分类控制器扩展；依赖 `nextEncodedFacetsURL` 与 `filters` 结构。

