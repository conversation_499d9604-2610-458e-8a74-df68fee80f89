---
description: 
globs: controllers/front/CategoryController.php
alwaysApply: false
---
### CategoryController（分类页控制器）规则

- 关联文件：
  - 控制器实现：[controllers/front/CategoryController.php](mdc:controllers/front/CategoryController.php)
  - 前台控制器基类：[classes/controller/FrontController.php](mdc:classes/controller/FrontController.php)
  - 分类聚合模型：[classes/category/CategoryModel.php](mdc:classes/category/CategoryModel.php)
  - 筛选模型：[classes/category/CategoryFilterModel.php](mdc:classes/category/CategoryFilterModel.php)
  - 产品模型（分页/排序/数据）：[classes/category/CategoryProductModel.php](mdc:classes/category/CategoryProductModel.php)
  - URL 模型：[classes/category/CategoryUrlModel.php](mdc:classes/category/CategoryUrlModel.php)
  - Facets 模型：[classes/category/CategoryFacetModel.php](mdc:classes/category/CategoryFacetModel.php)
  - 设备检测（Context）：[classes/Context.php](mdc:classes/Context.php)
  - 主题模板（PC）：[themes/ztheme/templates/catalog/category.tpl](mdc:themes/ztheme/templates/catalog/category.tpl)
  - 主题分页模板：[themes/ztheme/templates/_partials/ca-sea-pageination.tpl](mdc:themes/ztheme/templates/_partials/ca-sea-pageination.tpl)

## 职责边界（MVC）
- Controller：请求解析（`rewrite`/`id_category`）、生命周期调度、权限与错误页处理、模板选择、将模型数据传递给视图。
- Model：分类/筛选/产品/分页/URL 等业务逻辑（集中在 `CategoryModel` 系列）。
- View（Smarty）：只负责渲染；不得在控制器拼接 HTML。
- 禁止在控制器写 SQL；如需查询，放入相应 Model。

## 生命周期（基于 FrontController.run）
- `init()`（[CategoryController.php](mdc:controllers/front/CategoryController.php)）：
  - 优先读 GET `id_category`，否则通过 `CategoryModel::resolveCategoryIdByRewrite($rewrite,$idLang)` 解析分类 ID（[CategoryModel.php](mdc:classes/category/CategoryModel.php)）。
  - 构建 `Category`，校验 `active` 与 `checkAccess`；按需渲染 404/403 模板并返回。
  - 实例化 `CategoryModel`，调用 `initFilter()` 初始化筛选上下文。
  - 通过 `filterCategoryContent` Hook 允许模块链式过滤分类数据。
- `initContent()`：
  - 由 `CategoryModel::getProductsData()` 提供 `products/pages/current_page/total/product_ids`。
  - 由 `CategoryModel::getFacetsData()` 提供 `facets/removes/orders/...`。
  - 设备判断统一用 `$this->context->isMobile()` 切换模板：`catalog/mobile/category` 或 `catalog/category`。
- `getTemplateVarPage()`：
  - 404：`page_name=pagenotfound`、`body_classes.pagenotfound=true`。
  - 正常：注入 `category-id-{id}`、`category-{name}`、`category-id-parent-{id_parent}`、`category-depth-level-{level_depth}`。
- `canonicalRedirection()`：若分类有效，跳转到 canonical URL；受 `PS_CANONICAL_REDIRECT` 影响。

## URL 与分页规则
- 解析 `rewrite`：使用 `CategoryModel::resolveCategoryIdByRewrite()`（控制器禁止写 SQL）。
- URL 构造统一用 `CategoryUrlModel`：
  - `getFilterUrl($query_arr)`：返回含筛选段的前缀；
  - `getPageUrl($p)`：在当前筛选上下文拼接 `_p_{p}` 与排序段；
  - `getOrderUrl($key,$dir,$return_selected=false)`：生成排序 URL（或返回选中态）。
- 分页数据：使用 `CategoryProductModel::getPagesInfo($total,$baseUrl,$currentPage,$urlModel)` 构造 `pages`（[CategoryProductModel.php](mdc:classes/category/CategoryProductModel.php)）。
  - `nextURL` 必须形如 `{$baseUrl}{$urlModel->getPageUrl($p)}`，禁止仅返回域名或仅返回片段。
  - 典型 URL 示例：`/category-rewrite_filter_color_red,blue_length_long_price_50-200_p_2_order_price_dir_asc/`。

## 模板数据契约
- 必须注入（与 [category.tpl](mdc:themes/ztheme/templates/catalog/category.tpl) 对应）：
  - `label`：分类名；
  - `category`：分类对象（含 `image/link_rewrite/...`）；
  - `subcategories[]`：含 `url/name/image`；
  - `products[]`：含 `url/images/price/discount/is_ship_in_48hrs` 等；
  - `pages[]`：分页条数组，元素包含：`type(number|left_arrow|right_arrow|ellipsis)`、`page`、`selected`、`nextURL`；
  - `facets` 相关：`left_filters`、`ship_in_48hrs`、`color_group_filters`、`removes`、`orders`。

## 设备与模板
- 统一使用 `$this->context->isMobile()`（[Context.php](mdc:classes/Context.php)）。
- 移动端模板：`catalog/mobile/category`；PC 模板：`catalog/category`。

## 错误与权限处理
- 404：分类未加载或未激活；设置 404 头并 `setTemplate('errors/404')`。
- 403：无访问权限；设置 403 头并 `setTemplate('errors/forbidden')`。

## Hook 协作点
- `filterCategoryContent`：链式过滤分类数据；多模块串联时注意返回结构。
- 主题模板常见 Hook：`displayLeftColumn`、`displayProductListReviews`、`displayFooterCategory`。

## 允许的改动位（优先）
1. 新增/调整模板变量：在 `CategoryModel` 聚合；必要时通过 Hook 注入。
2. 新增筛选/调整 URL：修改 `CategoryUrlModel` 与 `CategoryFilterModel`，并保持 Facets 同步。
3. 分页/排序策略：在 `CategoryProductModel`；控制器仅传参。
4. 页面资源：如需分类页专属资源，覆盖控制器的 `setMedia()` 并用 `registerStylesheet/ registerJavascript`（参考 [FrontController.php](mdc:classes/controller/FrontController.php)）。

## 禁止/避免
- 禁止在控制器中写 SQL、拼接 HTML、手写完整 URL（除 canonical）。
- 禁止直接实例化 `Mobile_Detect`；统一用 `$this->context->isMobile()`。
- 禁止在控制器内部构造分页 URL；统一通过 `CategoryUrlModel`。

## 上线前自检清单
- URL：`rewrite/id_category` 解析是否兼容？筛选/排序/分页 URL 是否正确？`nextURL` 是否完整？
- 模板：`label/category/subcategories/products/pages/facets` 是否齐全？
- 设备：移动/PC 模板切换是否正确？
- 错误：403/404 头与模板是否正确？`getTemplateVarPage()` 的 `body_classes` 是否正确？
- 性能：是否有冗余查询/循环？是否影响 ES 查询负载？

