<?php
/**
 * Category Facet Model
 * 处理分类筛选条件相关的逻辑
 */
class CategoryFacetModel
{
    /**
     * @var Context
     */
    protected $context;

    /**
     * @var array 筛选条件
     */
    protected $query_arr = [];

    /**
     * @var Category
     */
    protected $category;

    /**
     * @var CategoryUrlModel
     */
    protected $urlModel;

    public function __construct($context, $query_arr, $category, $urlModel)
    {
        $this->context = $context;
        $this->query_arr = $query_arr;
        $this->category = $category;
        $this->urlModel = $urlModel;
    }

    /**
     * 传递筛选条件到模板
     * @param int $product_count
     * @param array $product_ids
     * @return array
     */
    public function getFacetsData($product_count = 0, $product_ids = [])
    {
        $assign_data = [
            'orders' => [],
            'facets' => [],
            'removes' => [],
        ];

        // 基础url
        $base_url = $this->context->link->getBaseLink();
        // 存已选中的筛选条件，用于移除
        $removes = [];
        // 清除所有筛选
        if (isset($this->query_arr['filter'])) {
            $removes['clear_all'] = $base_url . $this->category->link_rewrite . '/';
        }
        
        // 无价格筛选的url
        $range_urls = $this->urlModel->getRangeUrl();
        // 判断是否有价格筛选
        $range_selected = $this->urlModel->getRangeUrl(true);
        // 是否有价格筛选, 添加移除
        if ($range_selected) {
            $price_min = Tools::convertPrice($this->query_arr['filter']['ranges']['min']); // 转成当前货币价格
            $price_max = Tools::convertPrice($this->query_arr['filter']['ranges']['max']); // 转成当前货币价格
            $range_label = $this->context->currency->sign . $price_min . ' - ' . $this->context->currency->sign . $price_max;
            $removes['filters'][] = [
                'label' => $range_label,
                'remove_url' => $base_url . $range_urls['first_url'] . $range_urls['last_url']
            ];
        }

        $code = $this->getColorCode();
        
        // 已存在特性筛选
        if (isset($this->query_arr['filter']['features'])) {
            foreach ($this->query_arr['filter']['features'] as $feature_code => $feature_values) {
                //48小时单独增加按钮
                if ($feature_code == 'ship_in_48hrs') {
                    $removes['filters'][] = [
                        'label' => 'ship in 48hrs',
                        'remove_url' => $base_url . $this->urlModel->getFeatureUrl('ship_in_48hrs', 'ship-in-48hrs'),
                    ];
                    continue;
                }
                if($feature_code != $code){
                    foreach ($feature_values as $feature_value_urlname) {
                        $feature_value = Db::getInstance()->getValue(
                            'SELECT fvl.value FROM ' . _DB_PREFIX_ . 'feature_value fv
                            LEFT JOIN ' . _DB_PREFIX_ . 'feature_value_lang fvl ON (fvl.id_feature_value = fv.id_feature_value AND fvl.id_lang = ' . (int) $this->context->language->id . ')
                            WHERE fv.url_name = "' . $feature_value_urlname . '"');
                        $removes['filters'][] = [
                            'label' => $feature_value,
                            'remove_url' => $base_url . $this->urlModel->getFeatureUrl(strtolower($feature_code), strtolower($feature_value_urlname)),
                        ];
                    }
                    continue;
                }
                //是颜色单独处理
                if($feature_code == $code){
                    foreach ($feature_values as $attribute_name) {
                        //将Show as picture转成可识别的属性
                        $attributeName = Category::AttributeNameUrl($attribute_name);
                        $attribute_value = Db::getInstance()->getValue(
                            'SELECT al.name FROM ' . _DB_PREFIX_ . 'attribute a
                            LEFT JOIN ' . _DB_PREFIX_ . 'attribute_lang al ON (al.id_attribute = a.id_attribute AND al.id_lang = ' . (int) $this->context->language->id . ')
                            LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group_lang agl ON (agl.id_attribute_group = a.id_attribute_group)
                            WHERE al.name = "' . $attributeName . '"
                            AND agl.name = "color"');
                        $removes['filter_colors'][] = $removes['filters'][] = [
                            'label' => $attribute_value,
                            'remove_url' => $base_url . $this->urlModel->getFeatureUrl(strtolower($feature_code), strtolower($attribute_name)),
                        ];
                    }
                }
            }
        }
        $assign_data['removes'] = $removes;

        //无产品直接返回
        if ($product_count <= 0 || !$product_ids) {
            return $assign_data;
        }

        // 筛选
        $facets = [];
        // 判断first_url是否存在_filter_
        if (strpos($range_urls['first_url'], '_filter_') !== false) {
            $first_url = $range_urls['first_url'] . '_price_';
        } else {
            $first_url = $range_urls['first_url'] . '_filter_price_';
        }

        // 获取分类下所有产品的价格范围
        $product_range = Category::getProductPriceRange($product_ids);
        $min_price = 0;
        $max_price = 0;
        if ($product_range && is_array($product_range) && isset($product_range['min_price']) && $product_range['min_price'] !== null) {
            $min_price = Tools::convertPrice(floor($product_range['min_price'])); // 转成当前货币价格
        }
        if ($product_range && is_array($product_range) && isset($product_range['max_price']) && $product_range['max_price'] !== null) {
            $max_price = Tools::convertPrice(ceil($product_range['max_price'])); // 转成当前货币价格
        }
        // 价格范围筛选
        $range_price = [
            'label' => $this->context->getTranslator()->trans('Price Low To High', [], 'Shop.Theme.Catalog'),
            'type' => 'price',
            'widgetType' => 'price',
            'properties' => [
                'min' => $min_price,
                'max' => $max_price,
                'unit' => $this->context->currency->sign
            ],
            'nextEncodedFacetsURL' => [
                'first_url' => $base_url . $first_url,
                'last_url' => $range_urls['last_url']
            ],
            'current_selected' => [
                'min' => $min_price,
                'max' => $max_price
            ]
        ];

        // 是否有价格筛选, 添加移除
        if ($range_selected) {
            $range_price['current_selected']['min'] = Tools::convertPrice($this->query_arr['filter']['ranges']['min']); // 转成当前货币价格
            $range_price['current_selected']['max'] = Tools::convertPrice($this->query_arr['filter']['ranges']['max']); // 转成当前货币价格
        }
        $facets['left_filters'][] = $range_price;

        // 获取分类关联的特性
        $features = Category::getProductFeatures($product_ids);
        foreach ($features as $feature) {
            $widgetType = 'checkbox';
            // 获取所有的特性的筛选值
            $filters = [];
            foreach ($feature['values'] as $value) {
                // 图片
                $image = '';
                if (file_exists(_PS_FEATURE_VALUE_IMG_DIR_ . $value['id_feature_value'] . '.jpg')) {
                    $widgetType = 'checkbox_with_image';
                    $image = _PS_FEATURE_VALUE_IMG_ . $value['id_feature_value'] . '.jpg?v=' . mt_rand(100000, 999999);
                }
                $feature_selected = $this->urlModel->getFeatureUrl(strtolower($feature['code']), strtolower($value['url_name']), true);
                $feature_nextue_url = $base_url . $this->urlModel->getFeatureUrl(strtolower($feature['code']), strtolower($value['url_name']));
                $filters[] = [
                    'label' => $value['value'],
                    'type' => 'feature',
                    'selected' => $feature_selected,
                    'properties' => [
                        'image' => $image,
                        'color' => str_replace('-', '_', $value['url_name']),
                        'products_count' => $value['products_count'],
                    ],
                    'value' => $value['id_feature_value'],
                    'nextEncodedFacetsURL' => $feature_nextue_url
                ];
            }

            // 整理数据
            if ($feature['is_color']) {
                // 颜色
                $facets['color_filters'][] = [
                    'label' => $feature['name'],
                    'type' => 'feature',
                    'widgetType' => 'color', // 默认为checkbox
                    'properties' => [
                        'id_feature' => $feature['id_feature'],
                    ],
                    'filters' => $filters
                ];
            } else {
                // 左侧筛选
                $facets['left_filters'][] = [
                    'label' => $feature['name'],
                    'type' => 'feature',
                    'widgetType' => $widgetType, // 默认为checkbox
                    'properties' => [
                        'id_feature' => $feature['id_feature'],
                    ],
                    'filters' => $filters
                ];
            }
        }

        //除婚纱之外颜色筛选不显示
        $category_type = Category::getCategoryType($this->category->id);
        //获取产品所有的颜色属性
        if($category_type){
            $AttributeColors = Category::getProductAttributeColors($product_ids);
        }else{
            $facets['color_group_filters']['filters'] = [];
        }
        
        if(!empty($AttributeColors)){
            usort($AttributeColors, function ($a, $b) {
                return $b['acg_position'] <=> $a['acg_position'];
            });
            $filter_color_groups = [];
            //将所有的颜色整理到一起
            $filter_color_group_all = [];
            $image_baseurl = "https://" . _PS_IMAGE_DOMAIN_ . '/media/catalog/attribute-color-group/';
            foreach($AttributeColors as $AttributeColorKey => $AttributeColor){
                $image = '';
                if($AttributeColor['attribute_color_group_image_url'] != ''){
                    $image = $image_baseurl . $AttributeColor['attribute_color_group_image_url'];
                }
                $attribute_group_selected = $this->urlModel->getAttributeGroupUrl($code, strtolower($AttributeColor['attribute_names']), true);
                $attribute_group_nextue_url = $base_url . $this->urlModel->getAttributeGroupUrl($code, strtolower($AttributeColor['attribute_names']));
                $filter_color_groups[$AttributeColorKey] = [
                    'label' => $AttributeColor['attribute_color_group_name'],
                    'type' => 'attribute',
                    'selected' => $attribute_group_selected,
                    'acg_position'=>$AttributeColor['acg_position'],
                    'properties' => [
                        'image' => $image,
                        'color' => str_replace('-', '_', $AttributeColor['attribute_color_group_name']),
                        'products_count' => 0,
                    ],
                    'value' => $AttributeColorKey,
                    'nextEncodedFacetsURL' => $attribute_group_nextue_url,
                ];
                $filter_colors = [];
                foreach ($AttributeColor['attribute'] as $value) {
                    // 图片
                    $image = '';
                    if($value['image'] != ''){
                        $image = $image_baseurl . $value['image'];
                    }
                    $attribute_selected = $this->urlModel->getFeatureUrl($code, strtolower($value['url_attribute_name']), true);
                    $attribute_nextue_url = $base_url . $this->urlModel->getFeatureUrl($code, strtolower($value['url_attribute_name']));
                    $filter_colors[] = $filter_color_group_all[] = [
                        'label' => $value['attribute_name'],
                        'type' => 'attribute',
                        'selected' => $attribute_selected,
                        'properties' => [
                            'image' => $image,
                            'color' => str_replace('-', '_', $value['url_attribute_name']),
                            'products_count' => $value['products_count'],
                        ],
                        'value' => $value['id_attribute'],
                        'nextEncodedFacetsURL' => $attribute_nextue_url
                    ];
                }
                $filter_color_groups[$AttributeColorKey]['attribute_colors'] = $filter_colors;
            }
            //补上所有颜色筛选
            $filter_color_group_data = [
                'label' => 'ALL',
                'type' => 'attribute_all',
                'selected' => false,
                'properties' => [
                    'image' => _PS_ATTRIBUTE_COLOR_GROUPE_IMG_ .'All.png?v=' . mt_rand(100000, 999999),
                    'color' => 'all',
                    'products_count' => 0,
                ],
                'value' => 0,
                'nextEncodedFacetsURL' => '',
                'attribute_colors' => $filter_color_group_all
            ];
            array_push($filter_color_groups,$filter_color_group_data);
            if($filter_color_groups){
                foreach($filter_color_groups as $filter_color_group_key => $filter_color_group){
                    //选中颜色个数
                    $checkbox_num = 0;
                    foreach($filter_color_group['attribute_colors'] as $attribute_color){
                        if($attribute_color['selected'] == true){
                            $checkbox_num++;
                        }
                    }
                    $filter_color_groups[$filter_color_group_key]['checkbox_num'] = $checkbox_num;
                }
            }
            //颜色传到前台
            $facets['color_group_filters'] = [
                'label' => 'Color',
                'type' => 'attribute',
                'widgetType' => 'color', // 默认为checkbox
                'properties' => [
                    'id_attribute' => 0,
                ],
                'filters' => $filter_color_groups
            ];
        }

        // 有48小时发货的产品，增加一个48小时发货的筛选
        $products_count_48hours = Category::has48hoursProduct($product_ids);
        if ($products_count_48hours) {
            $feature_selected = $this->urlModel->getFeatureUrl('ship-in-48hrs', 'ship-in-48hrs', true);
            $feature_nextue_url = $base_url . $this->urlModel->getFeatureUrl('ship-in-48hrs', 'ship-in-48hrs');
            //左侧筛选增加48小时发货的筛选
            $facets['left_filters'][] = [
                'label' => 'ship in 48hrs',
                'type' => 'feature',
                'widgetType' => 'checkbox', // 默认为checkbox
                'properties' => [
                ],
                'filters' => [
                    [
                        'label' => 'ship in 48hrs',
                        'type' => 'feature',
                        'selected' => $feature_selected,
                        'properties' => [
                            'image' => '',
                            'color' => 'shipin48hours',
                            'products_count' => $products_count_48hours,
                        ],
                        'value' => 1,
                        'nextEncodedFacetsURL' => $feature_nextue_url
                    ]
                ]
            ];
            //右上筛选，增加48小时发货的筛选
            $facets['ship_in_48hrs'] = [
                'label' => 'ship in 48hrs',
                'nextEncodedFacetsURL' => $feature_nextue_url
            ];
        }
        $assign_data['facets'] = $facets;
        
        // 排序
        $orders = [
            [
                'label' => $this->context->getTranslator()->trans('New Arrivals', [], 'Shop.Theme.Catalog'),
                'selected' => $this->urlModel->getOrderUrl('recentlyadded', 'desc', true),
                'nextURL' => $base_url . $this->urlModel->getOrderUrl('recentlyadded', 'desc')
            ],
            [
                'label' => $this->context->getTranslator()->trans('Most Popular', [], 'Shop.Theme.Catalog'),
                'selected' => $this->urlModel->getOrderUrl('position', 'desc', true),
                'nextURL' => $base_url . $this->urlModel->getOrderUrl('position', 'desc')
            ],
            [
                'label' => $this->context->getTranslator()->trans('Price: Low to High', [], 'Shop.Theme.Catalog'),
                'selected' => $this->urlModel->getOrderUrl('price', 'asc', true),
                'nextURL' => $base_url . $this->urlModel->getOrderUrl('price', 'asc')
            ],
            [
                'label' => $this->context->getTranslator()->trans('Price: High to Low', [], 'Shop.Theme.Catalog'),
                'selected' => $this->urlModel->getOrderUrl('price', 'desc', true),
                'nextURL' => $base_url . $this->urlModel->getOrderUrl('price', 'desc')
            ],
        ];
        $assign_data['orders'] = $orders;
        
        //将筛选的个数传到前台
        $assign_data['removes']['filter_count'] = 0;
        if(!empty($assign_data['removes']) && !empty($assign_data['removes']['filters'])){
            $count_filters = count($assign_data['removes']['filters']);
            $assign_data['removes']['filter_count'] = $count_filters;
        }
        
        return $assign_data;
    }

    /**
     * 获取颜色代码
     * @return string
     */
    private function getColorCode()
    {
        if(isset($this->query_arr['filter']['features']['color']) && !empty($this->query_arr['filter']['features']['color'])){
            return 'color';
        }else{
            return 'dress_color';
        }
    }
} 