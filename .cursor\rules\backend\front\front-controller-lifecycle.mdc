---
description: 
globs: controllers/front/**/*Controller.php
alwaysApply: false
---
### FrontController 生命周期与改动指引

- **入口与调度**
  - 基类入口：`ControllerCore::run()`，前台实现见 [classes/controller/Controller.php](mdc:classes/controller/Controller.php)。
  - 若开启性能分析，包装类见 [tools/profiling/Controller.php](mdc:tools/profiling/Controller.php)，在 `run()` 前后 `stamp()` 并注入 profiling 面板。

- **关键阶段（前台）**: [classes/controller/FrontController.php](mdc:classes/controller/FrontController.php)
  1. `__construct()`：初始化 Presenter、Assets 管理器与 CCC 合并器。
  2. `init()`：
     - SSL 与 canonical 重定向：`sslRedirection()`、`canonicalRedirection()`。
     - 购物车/货币/客户与地理定位：`recoverCart()`、`geolocationManagement()`、`Tools::setCurrency()`。
     - 维护/限制页：`displayMaintenancePage()`、`displayRestrictedCountryPage()`。
  3. `initContent()`：
     - 注入全局变量：`assignGeneralPurposeVariables()`。
     - 执行业务：`process()`（可在子控制器覆写）。
     - Header Hook：`HOOK_HEADER = displayHeader`。
  4. `display()`：
     - 计算 `layout`、`stylesheets`、`javascript`、`js_custom_vars`、`notifications` 后渲染模板。

- **模板变量与页面信息**
  - 全局变量集中于 `assignGeneralPurposeVariables()`（购物车、站点、客户、像素 ID、埋点、搜索、热门搜索、控制器名/模块名、图片域等）。
  - 页面元信息：`getTemplateVarPage()`（meta、body classes、密码强度文案、多语言/税显式标签）。
  - URL 汇总：`getTemplateVarUrls()`（`pages` 表常用路由、`alternative_langs`、`actions.logout`、无图占位图）。

- **静态资源与缓存**
  - 使用 `registerStylesheet`/`registerJavascript`；自动适配媒体服务器与缓存策略。
  - 合并与压缩：`getStylesheets()`/`getJavascript()` 中基于 `PS_CSS_THEME_CACHE`/`PS_JS_THEME_CACHE` 触发 CCC。
  - 版本号：`setMedia()` 中通过 `'version' => $this->version` 控制缓存失效。
  - 移动端/RTL：基于 `Mobile_Detect` 与 `language->is_rtl` 条件加载不同样式。

- **常用挂点（Hook）**
  - `actionFrontControllerInitBefore/After`、`displayHeader`、`actionFrontControllerSetVariables`、`actionOutputHTMLBefore`、`displayOverrideTemplate`、`overrideLayoutTemplate` 等。

- **安全与跳转**
  - Token 校验：`isTokenValid()`；
  - 维护白名单 IP：`displayMaintenancePage()` 使用 `IpUtils::checkIp`；
  - Canonical 与 SSL 跳转会 `disallowWriting()` Cookie，注意改动跳转逻辑时的副作用。

- **建议的改动位**
  - 新增模板数据：优先在 `assignGeneralPurposeVariables()` 聚合，必要时通过 `actionFrontControllerSetVariables` Hook 注入。
  - 新增页面资源：在 `setMedia()` 按端（PC/移动）与优先级注册，并设置版本号。
  - 新增页面链接：在 `getTemplateVarUrls()` 的 `pages` 列表中统一维护。

  - 页面定制：覆写 `process()`/`getTemplateVarPage()` 或通过相应 Hook 定制。