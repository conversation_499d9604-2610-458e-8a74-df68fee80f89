<?php
/**
 * 调试脚本：检查订单状态配置
 */

// 包含 PrestaShop 配置
require_once __DIR__ . '/config/config.inc.php';

echo "=== 订单状态配置检查 ===\n\n";

// 检查关键订单状态的配置
$statuses = [
    '_PS_OS_CHEQUE_' => _PS_OS_CHEQUE_,
    '_PS_OS_PAYMENT_' => _PS_OS_PAYMENT_,
    '_PS_OS_ERROR_' => _PS_OS_ERROR_,
    '_PS_OS_BANKWIRE_' => _PS_OS_BANKWIRE_,
];

foreach ($statuses as $name => $id) {
    echo "状态常量: $name = $id\n";
    
    // 获取状态详细信息
    $order_state = new OrderState($id);
    if (Validate::isLoadedObject($order_state)) {
        echo "  名称: " . $order_state->name[1] . "\n";
        echo "  logable: " . ($order_state->logable ? 'YES' : 'NO') . "\n";
        echo "  invoice: " . ($order_state->invoice ? 'YES' : 'NO') . "\n";
        echo "  颜色: " . $order_state->color . "\n";
    } else {
        echo "  错误: 无法加载状态对象\n";
    }
    echo "\n";
}

echo "=== 检查 ps_order_payment 表结构 ===\n\n";

// 检查表结构
$sql = "DESCRIBE " . _DB_PREFIX_ . "order_payment";
$result = Db::getInstance()->executeS($sql);

if ($result) {
    foreach ($result as $field) {
        echo "字段: " . $field['Field'] . " | 类型: " . $field['Type'] . " | 可空: " . $field['Null'] . "\n";
    }
} else {
    echo "无法获取表结构\n";
}

echo "\n=== 检查最近的订单支付记录 ===\n\n";

// 检查最近的订单支付记录
$sql = "SELECT op.*, o.reference as order_ref, o.current_state 
        FROM " . _DB_PREFIX_ . "order_payment op 
        LEFT JOIN " . _DB_PREFIX_ . "orders o ON op.order_reference = o.reference 
        ORDER BY op.date_add DESC 
        LIMIT 5";

$payments = Db::getInstance()->executeS($sql);

if ($payments) {
    foreach ($payments as $payment) {
        echo "订单号: " . $payment['order_ref'] . "\n";
        echo "  支付金额: " . $payment['amount'] . "\n";
        echo "  交易号: " . ($payment['transaction_id'] ?: '空') . "\n";
        echo "  卡号: " . ($payment['card_number'] ?: '空') . "\n";
        echo "  支付方式: " . ($payment['payment_method'] ?: '空') . "\n";
        echo "  订单状态: " . $payment['current_state'] . "\n";
        echo "  创建时间: " . $payment['date_add'] . "\n";
        echo "\n";
    }
} else {
    echo "没有找到支付记录\n";
}

echo "=== 完成 ===\n";
