---
description: 
globs: themes/ztheme/**/*.html,themes/ztheme/**/*tpl
alwaysApply: false
---
### 前台模板与静态资源

- **生命周期与入口**
  - 前台控制器： [classes/controller/FrontController.php](mdc:classes/controller/FrontController.php)
  - 生命周期由基类 [classes/controller/Controller.php](mdc:classes/controller/Controller.php) 的 `run()` 驱动：`init → setMedia → postProcess → initHeader → initContent → initFooter → display`

- **模板变量注入（Smarty）**
  - 全局变量集中在 `assignGeneralPurposeVariables()` 中注入。
  - 页面结构变量可从 `getTemplateVar*()` 获取：如 `getTemplateVarUrls()`、`getTemplateVarShop()`、`getTemplateVarPage()`。
  - 若需新增全局变量，优先扩展 `assignGeneralPurposeVariables()`，或通过相关 Hook（如 `actionFrontControllerSetVariables` 若存在）在不改核心的前提下注入。

- **URL 与重定向**
  - 规范 URL：`canonicalRedirection()` 受配置 `PS_CANONICAL_REDIRECT` 影响。
  - SSL 策略：`sslRedirection()` 受 `PS_SSL_ENABLED` 与 `PS_SSL_ENABLED_EVERYWHERE` 影响。
  - 常用页面链接集中于 `getTemplateVarUrls()`，需要统一路由或外链时优先改这里。

- **静态资源注册**
  - 推荐使用新接口：`registerStylesheet` / `registerJavascript`（见 `setMedia()`）。
  - 参数常见项：`id`、`path`、`priority`、`media`、`inline`、`position`（head/bottom）、`version` 等。
  - 旧接口 `addCSS`/`addJS` 尽量避免新开发中继续使用，保持兼容即可。
  - 主题资源合并/压缩（CCC）相关配置：`PS_CSS_THEME_CACHE`、`PS_JS_THEME_CACHE`。
  - 若仅对单一页面生效，建议在对应前台控制器覆盖 `setMedia()` 并按条件注册；若跨页面通用，考虑在主题主入口或全局 Hook 中注册。

- **主题与模板路径**
  - 主题位于 [themes/](mdc:themes)。模块模板一般在 `modules/<module>/views/templates/` 下，主题可通过 override 机制覆盖模块模板。

- **调试与性能**
  - Profiling 包装控制器： [tools/profiling/Controller.php](mdc:tools/profiling/Controller.php)。开启后可观察生命周期耗时与资源注册顺序。

- **常见改动建议**
  - 全局变量/链接结构：修改 `assignGeneralPurposeVariables()`、`getTemplateVarUrls()`。
  - 页面专属样式/脚本：覆盖对应控制器 `setMedia()` 并调用 `registerStylesheet/ registerJavascript`。
  - 缓存失效：变更静态资源后，使用 `version` 参数或调整主题缓存配置以促使客户端更新。

